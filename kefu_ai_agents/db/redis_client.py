from typing import Optional, List, Dict, Union
import os
import time
import json
from redis.cluster import RedisCluster, ClusterNode
from redis.exceptions import RedisError, ConnectionError
from kefu_ai_agents.utils.loguru_config import get_logger
from dotenv import load_dotenv
import threading

# 加载环境变量 (考虑延迟加载)
load_dotenv()

# 初始化日志
logger = get_logger(__name__)


class RedisClient:
    """Redis 集群客户端单例"""

    _instance = None
    _lock = threading.Lock()  # 添加线程锁

    def __new__(cls, *args, **kwargs) -> "RedisClient":
        """实现线程安全的单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(RedisClient, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(
        self,
        startup_nodes: Optional[List["ClusterNode"]] = None,
        password: Optional[str] = None,
        prefix: Optional[str] = None,
        max_connections: int = 50,
        socket_timeout: int = 5,
        socket_connect_timeout: int = 5,
        retry_on_timeout: bool = False,
        max_attempts: int = 3,
        decode_responses: bool = True,
    ):
        """初始化 Redis 集群连接"""
        with self._lock:  # 线程安全初始化
            if self._initialized:
                return

            # 设置基本配置
            self.prefix = (
                prefix if prefix is not None else os.getenv("REDIS_PREFIX", "")
            )
            self.startup_nodes = startup_nodes or self._get_startup_nodes_from_env()
            self.password = password or os.getenv("REDIS_PASSWORD")
            self.cluster_name = os.getenv("REDIS_CLUSTER_NAME", "default_cluster")
            self.client = None

            try:
                # 创建 Redis 集群客户端
                self.client = RedisCluster(
                    startup_nodes=self.startup_nodes,
                    password=self.password,
                    max_connections=max_connections,
                    socket_timeout=socket_timeout,
                    socket_connect_timeout=socket_connect_timeout,
                    # 可能需要减少重试次数或禁用重试
                    retry_on_timeout=retry_on_timeout,
                    max_attempts=max_attempts,
                    decode_responses=decode_responses,
                )

                # 测试连接
                self.client.ping()
                self._initialized = True
                self._connection_error = False
                logger.info("Redis 集群 [%s] 连接初始化成功", self.cluster_name)
            except RedisError as error:
                self._initialized = False  # 确保初始化失败时正确设置标志
                self._connection_error = True
                logger.error(
                    "初始化 Redis 集群 [%s] 连接失败：%s",
                    self.cluster_name,
                    error,
                    exc_info=True,
                )

    def _get_startup_nodes_from_env(self) -> List["ClusterNode"]:
        """从环境变量获取集群节点配置"""
        nodes_str = os.getenv("REDIS_CLUSTER_NODES", "localhost:6379")
        nodes = [node.strip() for node in nodes_str.split(",")]

        startup_nodes = []
        for node_str in nodes:
            host, port = node_str.split(":")
            startup_nodes.append(ClusterNode(host=host, port=int(port)))

        return startup_nodes

    @classmethod
    def get_instance(cls, **kwargs) -> "RedisClient":
        """获取 RedisClient 单例实例"""
        if cls._instance is None or not getattr(cls._instance, "_initialized", False):
            cls._instance = cls(**kwargs)
        return cls._instance

    def check_health(self) -> bool:
        """检查连接健康状态"""
        try:
            return bool(self.client.ping())
        except (ConnectionError, RedisError) as error:
            logger.error(
                "Redis 集群 [%s] 健康检查失败：%s",
                self.cluster_name,
                error,
                exc_info=True,
            )
            return False

    def close(self) -> None:
        """关闭连接"""
        if hasattr(self, "client"):
            self.client.close()
            logger.info("Redis 集群 [%s] 连接已关闭", self.cluster_name)

    def _prefixed_key(self, key: str) -> str:
        """为键添加前缀"""
        return f"{self.prefix}{key}" if self.prefix else key

    # === 核心 Redis 操作 ===

    def set_value(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置键值"""
        try:
            return self.client.set(name=self._prefixed_key(key), value=value, ex=ex)
        except RedisError as error:
            logger.error("设置键值出错：%s", error, exc_info=True)
            return False

    def get_value(self, key: str) -> Optional[str]:
        """获取键值"""
        try:
            return self.client.get(name=self._prefixed_key(key))
        except RedisError as error:
            logger.error("获取键值出错：%s", error, exc_info=True)
            return None

    def original_get(self, key: str) -> Optional[str]:
        """获取键值 - 原始方法"""
        try:
            return self.client.get(name=key)
        except RedisError as error:
            logger.error("获取键值出错：%s", error, exc_info=True)
            return None

    def exists(self, *keys: str) -> int:
        """检查键是否存在"""
        try:
            prefixed_keys = [self._prefixed_key(key) for key in keys]
            return self.client.exists(*prefixed_keys)
        except RedisError as error:
            logger.error("检查键存在出错：%s", error, exc_info=True)
            return 0

    def delete(self, *keys: str) -> int:
        """删除键"""
        try:
            prefixed_keys = [self._prefixed_key(key) for key in keys]
            return self.client.delete(*prefixed_keys)
        except RedisError as error:
            logger.error("删除键出错：%s", error, exc_info=True)
            return 0

    def get_latest_key_by_pattern(
        self, pattern: str, latest_num: int = 1
    ) -> Optional[Union[List[str], str]]:
        """获取匹配指定模式的最新键

        Args:
            pattern: 匹配模式
            latest_num: 返回最新键的数量，默认为1

        Returns:
            当latest_num=1时，返回单个键或None；当latest_num>1时，返回键列表或空列表
        """
        try:
            # 应用前缀到模式
            prefixed_pattern = self._prefixed_key(pattern)
            logger.debug("获取最新键：%s", prefixed_pattern)
            matching_keys = []

            # 扫描匹配模式的所有键
            for key in self.client.scan_iter(match=prefixed_pattern):
                matching_keys.append(key)

            if matching_keys:
                # 排序键以获取最新的(假设键包含时间戳格式)
                matching_keys.sort(reverse=True)

                # 根据latest_num返回适当的结果
                if latest_num == 1:
                    return matching_keys[0]
                else:
                    return matching_keys[: min(latest_num, len(matching_keys))]

            # 根据latest_num返回适当的空结果
            return None if latest_num == 1 else []
        except RedisError as error:
            logger.error("获取最新键出错：%s", error, exc_info=True)
            return None if latest_num == 1 else []

    def get_latest_value_by_pattern(
        self, pattern: str, latest_num: int = 1
    ) -> List[Dict]:
        """获取匹配指定模式的最新值

        Args:
            pattern: 匹配模式
            latest_num: 返回最新值的数量，默认为1

        Returns:
            当latest_num=1时，返回单个值或None；当latest_num>1时，返回值列表或空列表
        """
        latest_keys = self.get_latest_key_by_pattern(pattern, latest_num)
        if latest_keys:
            if latest_num == 1:
                return (
                    [json.loads(self.client.get(latest_keys))]
                    if self.client.get(latest_keys)
                    else None
                )
            else:
                return [
                    json.loads(self.client.get(key))
                    for key in latest_keys
                    if self.client.get(key)
                ]
        return [] if latest_num > 1 else None


# 示例使用
if __name__ == "__main__":
    try:
        # 默认配置 - 从环境变量获取
        redis_client = RedisClient.get_instance()

        # 基本操作
        redis_client.set_value("test_key", "test_value", ex=60)  #
        value = redis_client.get_value("test_key")
        logger.info("获取到的值: %s", value)

        # 应用结束时关闭
        # redis_client.close()
    except Exception as exc:
        logger.error("Redis 操作异常: %s", exc)
