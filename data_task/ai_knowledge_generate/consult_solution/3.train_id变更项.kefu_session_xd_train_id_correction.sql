-- ******************************************************************** --
-- Author: 陈晗
-- Comment: 请输入业务注释信息
-- 这个表 dm.kefu_tb_yx_prod_intention 需要修改为线上业务表的同步ods表(done)
-- ******************************************************************** --
insert
  OVERWRITE table data_mining.kefu_session_xd_train_id_correction PARTITION (ds = "${azkaban.flow.1.days.ago}")
select
  nvl(t1.train_id_raw, t2.train_id_new) as train_id_new,
  t1.cluster_cate,
  t2.trainid as train_id_old,
  t2.clusterintent,
  t2.correctedintent
from
  (
    select
      *
    from
      dim.dim_kf_train_task_xd_cluster
    where
      ds = "${azkaban.flow.2.days.ago}"
  ) t1
  full join (
    select
      b.train_id as train_id_new,
      a.*
    from
      (
        select
          *
        from
          ods.ods_db_yanxuan_kefu_tb_yx_prod_intention_p -- dm.kefu_tb_yx_prod_intention(临时)
        where
          ds = "${azkaban.flow.1.days.ago}"
      ) a
      left join (
        select
          CONCAT(
            "XD-",
            itemid,
            "-",
            SUBSTRING(REPLACE(UUID(), '-', ''), 1, 8)
          ) as train_id,
          platformcode,
          itemid,
          correctedintent
        from
          ods.ods_db_yanxuan_kefu_tb_yx_prod_intention_p -- dm.kefu_tb_yx_prod_intention(临时)
        where
          ds = "${azkaban.flow.1.days.ago}"
        group by
          platformcode,
          itemid,
          correctedintent
      ) b on a.itemid = b.itemid
      and a.correctedintent = b.correctedintent
      and a.platformcode = b.platformcode
  ) t2 on t1.item_id = t2.itemid
  and t1.channel = t2.platformcode
  and t1.cluster_cate = t2.correctedintent
where
  t2.trainid is not NULL