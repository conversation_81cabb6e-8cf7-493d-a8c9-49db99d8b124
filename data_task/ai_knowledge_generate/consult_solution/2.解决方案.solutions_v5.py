from pyspark.sql import SparkSession
import json
import requests
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import re
import sys
import logging
import uuid
import time
logger = logging.getLogger(__name__)


spark = SparkSession.builder.appName("bdms_chenhan04")\
                    .config("spark.debug.maxToStringFields","1000")\
                    .config("spark.driver.maxResultSize", "30g")\
                    .enableHiveSupport()\
                    .getOrCreate()

def obtain_cluster_data(ds,channel):
    sql="""
        select
          t1.*,t2.session_message_content
        from
          (
            select
              distinct shop_name,
                item_id,
                sku_id,
                item_name,
                session_id,
                cate1,
                cate2,
                cate3,
                voc,
                cluster_cate,
                channel,
                yw_ds
            from
              data_mining.kefu_session_xd_cluster_category_final
            where
              ds = "{}"
              and channel = "{}"
              and cluster_cate is not NULL
          ) t1
          left join (
            select
              distinct session_id,
              session_message_content
            from
              data_mining.kf_session_message_content_detail_ch
            where
              ds = "{}"
          ) t2 on t1.session_id = t2.session_id
    
    """.format(ds,channel,ds)
    pd=spark.sql(sql).toPandas()
    return pd


def llm_chat(system_prompt,user_prompt):
    url = 'http://127.0.0.1:8550/proxy/online.yanxuan-nlp-server.service.mailsaas/openai/chat'
    headers = {
        'Content-Type': 'application/json',
        'product': 'mock',
        'service': 'deepseek-r1',
        'timeout': '600000'
    }
    payload = {
        "messages": [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": user_prompt
            },
            
        ],
        "model": "deepseek-r1",
        "maxTokens": 10001,
        "temperature": 0.2,
        "topP": 0.6,
        "presencePenalty": 0,
        "frequencyPenalty": 0
    }

    response = requests.post(url, json=payload, headers=headers)

    return json.loads(response.text)

# 请求intellibot获取结果
def obtain_bot_faq(item_id,question, channel_id):
    # 灌item_id的接口，模拟会话商品卡片
    url_add="http://127.0.0.1:8550/proxy/online.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/multi_channel_qa_addi.do"
    # 获取答案的接口，模拟用户问题
    url_qa="http://127.0.0.1:8550/proxy/online.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/multi_channel_qa.do"

    # 通用请求头
    headers = {
        'Content-Type': 'application/json'
    }
    
    session_id="cluster_xd_{}".format(str(uuid.uuid4().hex))
    
    message_id1="cluster_xdm_{}".format(str(uuid.uuid4().hex))
    message_id2="cluster_xdm_{}".format(str(uuid.uuid4().hex))
    
    
    # 灌item_id的数据
    payload_addi={
        "platform":"", # 全平台
        "channelId":channel_id, # 全渠道
        "sessionId":session_id,
        "messageId":message_id1,
        "rawMsg":"",
        "itemId":item_id
    }

    payload_qa={
        "platform":"", # 全平台
        "channelId":channel_id, # 全渠道
        "sessionId":session_id,
        "messageId":message_id2,
#         "rawMsg":str({"text":"{}".format(question)}),
        "rawMsg":"{\"text\":\""+question+"\"}",
        "messageSource":1,
        "messageType":1,
        "sessionInteraction":0
    }
#     print(payload_qa)
    
    
    response_addi = json.loads(requests.post(url_add, json=payload_addi, headers=headers).text)
#     print(response_addi)
    
    response_qa = json.loads(requests.post(url_qa, json=payload_qa, headers=headers).text)
#     print(response_qa)
    
    
    return response_qa['data']['faqList']

def get_item_id(item_name,ds):
    sql="""
        select distinct item_id from dim.dim_sku_p where ds='{}' and item_name='{}'
    """.format(ds,item_name)
    item_id=spark.sql(sql).toPandas()["item_id"].unique().tolist()
    if len(item_id)!=0:
        return int(item_id[0])
    else:
        return 0

def query_parser(sentence):
    # 去除对话前面的数字
    bye_sentence_words=["祝愿","祝你","爱你","爱您","服务宗旨","效劳"]
    if '\r\n' in sentence:
        dialogue_part= sentence.split('\r\n')
        processed_lines = [re.sub(r'^\d{4}', '', line) for line in dialogue_part]
        processed_lines = [re.sub(r'https?://\S+|www\.\S+', '...', line) for line in processed_lines]
        pattern = re.compile("|".join(map(re.escape, bye_sentence_words)))
        processed_lines = [line for line in processed_lines if not pattern.search(line)]
        qp_sen="/n".join(processed_lines)
        return qp_sen
    return sentence
    
def sanitize_hive_data(texts):
    # 匹配所有控制字符、空格及Hive默认分隔符
    rt=[]
    for text in texts:
        pattern = r'[\s\x00-\x1F\x7F]'
        rt.append(re.sub(pattern, '', text))
    return rt
    
def sanitize_hive_data2(texts):
    # 匹配所有控制字符、空格及Hive默认分隔符
    pattern = r'[\s\x00-\x1F\x7F]'
    rt=re.sub(pattern, '', texts)
    return rt

def solution_task(item_name,pd_cluster,channel_ids,ds):
    if not channel_ids:
        raise ValueError("店铺ID列表不能为空，请检查平台和渠道配置。")
    
    cluster_list=pd_cluster.loc[pd_cluster["item_name"]==item_name]["cluster_cate"].unique().tolist()
    item_id=get_item_id(item_name,ds)
    
    for cluster_cate in cluster_list:
        result_dic=[]
        result_dic_kf=[]
        # try:
        sessions=pd_cluster.loc[(pd_cluster["item_name"]==item_name) & (pd_cluster["cluster_cate"]==cluster_cate)][["session_id","session_content"]].drop_duplicates().to_dict('records')
        session_ids=pd_cluster.loc[(pd_cluster["item_name"]==item_name) & (pd_cluster["cluster_cate"]==cluster_cate)]["session_id"].unique().tolist()
        session_ids_str='|'.join([str(i) for i in session_ids])
        user_prompt_full=user_prompt.format(cluster_cate,sessions)
        print(user_prompt_full)
        response=llm_chat(sys_promot,user_prompt_full)
        response_txt=response['data']["detail"]["choices"][0]["message"]["content"]
        solutions=json.loads(response_txt[response_txt.find("{"):response_txt.rfind("}")+1])
        print(solutions)
        knowledge_solutions = []
        product_solutions = []

        # 遍历并分类
        uid=uuid.uuid4().hex[0:8]
        solution_uid=uuid.uuid4().hex[0:8]
        for item in solutions['solution']:
            if item['角度'] == '知识库':
                solution_id_1="{}01".format(solution_uid)
                

                knowledge_id_list=[]
                answer_id_list=[]
                attribute_id_list=[]
                attribute_valueid_list=[]
                attribute_name_list=[]
                faq_list=[]

                for channel_id in channel_ids:
                    # obtain_bot_faq(4104527,"保质期多久")
                    faq=obtain_bot_faq(item_id,cluster_cate, channel_id)

                    for faq_item in faq:
                        if faq_item["knowledgeSource"]==1:
                            answerId = str(faq_item["answerId"])
                            if answer_id_list and answerId in answer_id_list:
                                continue
                            knowledge_id_list.append(str(faq_item["knowledgeId"]))
                            answer_id_list.append(answerId)
                            faq_list.append(faq_item)
                        if faq_item["knowledgeSource"]==2:
                            attributeValueId = str(faq_item["answerId"])
                            if attribute_valueid_list and attributeValueId in attribute_valueid_list:
                                continue
                            attribute_id_list.append(str(faq_item["knowledgeId"]))
                            attribute_valueid_list.append(attributeValueId)
                            attribute_name_list.append(str(faq_item["showQuestion"]))
                            faq_list.append(faq_item)
                    
                row_data=[]
                if len(knowledge_id_list)!=0:
                    row_data.append([1,"|".join(knowledge_id_list),"|".join(answer_id_list),""])
                elif len(attribute_id_list)!=0:
                    row_data.append([2,"|".join(attribute_id_list),"|".join(attribute_valueid_list),"|".join(attribute_name_list)])
                else:
                    row_data.append([-1,"","",""])

                for row_item in row_data:
                    result_dic.append(
                            {
                                "train_id_raw":"XD-{}-{}".format(item_id,uid),
                                "item_id":item_id,
                                "item_name":item_name,
                                "cluster_cate":cluster_cate,
                                "solution_type":item['角度'],
                                "solutions":item['方案'],
                                "sessions":solutions['sessions'],
                                "session_ids":session_ids_str,
                                "kf_answer":solutions['kf_answer'],
                                "source_type":row_item[0],
                                "knowledge_id":row_item[1],
                                "answer_id":row_item[2],
                                "attr":row_item[3], # 仅知识库角度返回商品属性的时候才有值

                            }
                        )
                # 这边添加
                # 对bot内容进行精简一些
                faq_bot=[{"knowledgeType":row["knowledgeSource"],"knowledgeId":row["knowledgeId"],"answerId":row["answerId"],"itemId":row["itemId"] if row["knowledgeSource"]==2 else -1} for row in faq_list]
                result_dic_kf.append(
                    {
                        "train_id":"XD-{}-{}".format(item_id,uid),
                        "item_id":item_id,
                        "item_name":item_name,
                        "cluster_cate":cluster_cate,
                        "solution_type":1,
                        "solution_id":solution_id_1,
                        "solutions":sanitize_hive_data2(item['方案']["答案"]),
                        "bot_response":str(faq_bot)
                    }
                )
            elif item['角度'] == '商品':
                solution_id_2="{}02".format(solution_uid)
                result_dic.append(
                        {
                            "train_id_raw":"XD-{}-{}".format(item_id,uid),
                            "item_id":item_id,
                            "item_name":item_name,
                            "cluster_cate":cluster_cate,
                            "solution_type":item['角度'],
                            "solutions":item['方案'],
                            "sessions":solutions['sessions'],
                            "session_ids":session_ids_str,
                            "kf_answer":sanitize_hive_data(solutions['kf_answer']),
                            "source_type":-1,
                            "knowledge_id":"",
                            "answer_id":"",
                            "attr":""
                        }
                    )
                result_dic_kf.append(
                    {
                        "train_id":"XD-{}-{}".format(item_id,uid),
                        "item_id":item_id,
                        "item_name":item_name,
                        "cluster_cate":cluster_cate,
                        "solution_type":2,
                        "solution_id":solution_id_2,
                        "solutions":sanitize_hive_data2(item['方案']),
                        "bot_response":"[]"
                    }
                )
        # except Exception as e:
        #         logging.error(e)
        #         continue
#     return result_dic
    
        print(result_dic)
        # df = pd.DataFrame(result_dic).dropna()
        df = pd.DataFrame(result_dic)
        df["sessions"]=df["sessions"].astype('string')
        df["solutions"]=df["solutions"].astype('string')
        df["kf_answer"]=df["kf_answer"].astype('string')
        solution_sp=spark.createDataFrame(df)
        solution_sp.createOrReplaceTempView("data_solution")
        sql0="""insert into table data_mining.kefu_session_xd_cluster_category_solutions PARTITION (ds="{}",channel="{}")
                select * from data_solution""".format(ds,channel)
        spark.sql(sql0)
        
        
        df_kf = pd.DataFrame(result_dic_kf)
        df_kf["solutions"]=df_kf["solutions"].astype('string')
        solution_sp_kf=spark.createDataFrame(df_kf)
        solution_sp_kf.createOrReplaceTempView("solution_sp_kf")
        sql1="""insert into table data_mining.kefu_session_xd_cluster_category_solutions_kf_temp PARTITION (ds="{}",channel="{}")
                select * from solution_sp_kf""".format(ds,channel)
        spark.sql(sql1)
    return



sys_promot="""
### 角色
你是专业的数据分析和电商运营人员

### 简介
你可以根据用户给的问题总结以及一些列和这些问题相关的用户和和客服的会话，分析用户未下单的原因，并总结出可能的解决方案，解决方案需要从商品角度和知识库补充角度给出

### 限制
1.输出结果必须积极健康，切忌任何反动、色情、暴力等回复
2.解决方案不输出华而不实的内容，一定短时间可实现、好实现的，杜绝搭建xx系统，组织xx部门这类可操作性不强的解决方案
3.解决方案不要出现难懂的词汇,需要做到通俗易懂

### 能力详细说明
1. 接受用户的关于一类会话的分类，并且该分类下有多个会话，根据分类判断用户未下单原因
3. 基于分析到的原因，给出有效的解决方案，解决方案从商品角度、知识库补充角度进行给出，给出的解决方案必须和会话分类有关,杜绝和会话分类无关的解决方案和知识
4. 商品角度包括，增加某个类目，商详页添加某个说明等等，知识库角度基于会话分类，在多个会话中寻找答案，并组织成一句话
5. 每个会话仅给出一个可能补充的知识库和一个商品角度的解决方案，每个解决方案字数不超过100
6. 如果是知识库补充，需要给会话分类的答案，将会话分类这个字段看做是用户的问题，同时每一个会话分类需要你提取出客服的回答内容，并从客服角度重新整合输出结果
7. 给的每一个会话都可以提取会话信息，用来佐证你上面的提供的解决方案。

"""

user_prompt="""
我有会话分类{},涉及到的会话为{},请帮我进行数据分析,并生成解决方案,注意客服的回答不一定对，需要你进行取舍，输出结果以json格式给出，输出格式为
{{"solution":[{{"角度":"商品","方案":""}},{{"角度":"知识库","方案":{{"答案":""}}}}}}],"kf_answer":["","",...],"sessions":[{{"session_id":"","session_content":["'用户':'','客服':''","...","..."]}}]}},
其中各个参数的说明为:solution为解决方案，包含两种，商品角度和知识库角度，商品角度方案基于原文给出直一段文字解决方案，知识库角度需要有答案对,知识库角度仅需要一个最有效的,需要根据客服的话进行总结,kf_answer是提取出来的客服回答的原文（需要原文，并且仅输出客服的话，保存在列表中），
并且你给出的商品和知识库必须有具体的会话内容为依据，session是每个会话都需要给出用户和客服的会话对，需要有回话内容和回话id,会话内容和会话id不可乱编。
"""


if __name__ == "__main__":
#     channel="dy"
    channel=sys.argv[1]
    # yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
#     yesterday="2025-04-07"
    yesterday=sys.argv[2]
    pd_cluster=obtain_cluster_data(yesterday,channel)
    pd_cluster["session_content"]=pd_cluster["session_message_content"].apply(lambda row:query_parser(row))
    item_names=pd_cluster["item_name"].unique().tolist()
    
    # 平台对应的店铺ID
    platform_channel_ids = {
        "tm":[1990361],
        "dy":[6900998, 5995821, 6903256, 6901857],
        "jd":[6800324],
    }
    channel_ids = platform_channel_ids.get(channel, [])
    
    # 每次重跑，删除之前的数据
    sql_dd="""ALTER TABLE data_mining.kefu_session_xd_cluster_category_solutions DROP IF EXISTS PARTITION (ds="{}",channel="{}")""".format(yesterday,channel)
    spark.sql(sql_dd)
    
    sql_dd2="""ALTER TABLE data_mining.kefu_session_xd_cluster_category_solutions_kf_temp DROP IF EXISTS PARTITION (ds="{}",channel="{}")""".format(yesterday,channel)
    spark.sql(sql_dd2)
    
    with ThreadPoolExecutor(max_workers=20) as executor:
        # 提交任务
        futures = [executor.submit(solution_task, item_name,pd_cluster,channel_ids,yesterday) for item_name in item_names] 
        # 获取结果
        # for future in futures:
            # print(future.result())