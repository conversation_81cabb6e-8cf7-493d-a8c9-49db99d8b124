-- ******************************************************************** --
-- Author: 陈晗
-- Comment: 请输入业务注释信息
-- ******************************************************************** --
drop table if exists temp.temp_kefu_session_xd_cluster_category_solutions_update_kf;
create table temp.temp_kefu_session_xd_cluster_category_solutions_update_kf as
select
  COALESCE(t2.train_id_raw, t1.train_id) as train_id,
  t1.item_id,
  t1.item_name,
  t1.cluster_cate,
  t1.solution_type,
  t1.solution_id,
  t1.solutions,
  t1.bot_response,
  t1.channel
from
  (
    select
      *
    from
      data_mining.kefu_session_xd_cluster_category_solutions_kf_temp
    where
      ds = "${azkaban.flow.1.days.ago}"
  ) t1
  left join (
    select
      *
    from
      dim.dim_kf_train_task_xd_cluster
    where
      ds = "${azkaban.flow.2.days.ago}"
  ) t2 on t1.cluster_cate = t2.cluster_cate
  and t1.item_id = t2.item_id
  and t1.channel = t2.channel