-- ******************************************************************** --
-- Author: 陈晗
-- CreateTime: 2025-05-15 15:55:35
-- ******************************************************************** --
insert
  OVERWRITE table data_mining.kefu_session_xd_cluster_category_solutions_update PARTITION (ds = "${azkaban.flow.1.days.ago}", channel)
select
  COALESCE(t2.train_id_raw, t1.train_id_raw) as train_id,
  t1.item_id,
  t1.item_name,
  t1.cluster_cate,
  t1.solution_type,
  t1.solutions,
  t1.sessions,
  t1.session_ids,
  t1.kf_answer,
  t1.source_type,
  t1.knowledge_id,
  t1.answer_id,
  t1.attr,
  t1.channel
from
  (
    select
      *
    from
      data_mining.kefu_session_xd_cluster_category_solutions
    where
      ds = "${azkaban.flow.1.days.ago}"
  ) t1
  left join (
    select
      *
    from
      dim.dim_kf_train_task_xd_cluster
    where
      ds = "${azkaban.flow.2.days.ago}"
  ) t2 on t1.cluster_cate = t2.cluster_cate
  and t1.item_id = t2.item_id
  and t1.channel = t2.channel