-- ******************************************************************** --
-- Author: 陈晗
-- Comment: 请输入业务注释信息 更新前一天的数据
-- ******************************************************************** --

insert
  OVERWRITE table dim.dim_kf_train_task_xd_cluster PARTITION (ds = "${azkaban.flow.2.days.ago}")
select
  distinct nvl(b.train_id_new, a.train_id_raw) as train_id_raw,
  a.channel,
  nvl(b.correctedintent,a.cluster_cate) as cluster_cate,
  a.item_id,
  "无" as solution_type,
  "" as first_ds,
  0 as is_check
from
  (
    select
      *
    from
      dim.dim_kf_train_task_xd_cluster
    where
      ds = "${azkaban.flow.2.days.ago}"
  ) a
  left join (
    select * from data_mining.kefu_session_xd_train_id_correction where ds="${azkaban.flow.1.days.ago}"
  ) b on a.train_id_raw = b.train_id_old